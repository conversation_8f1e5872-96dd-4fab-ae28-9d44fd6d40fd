package com.docs.files.service.strategy;

import com.docs.common.core.domain.AjaxResult;
import com.docs.common.exception.ServiceException;
import com.docs.common.jooq.generated.enums.FilesFileType;
import com.docs.common.jooq.generated.enums.FilesStatus;
import com.docs.common.jooq.generated.tables.pojos.Files;
import com.docs.common.jooq.generated.tables.pojos.ProductModels;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;

/**
 * 研发文件处理策略，处理研发文件的上传
 * 研发文件是独立于图纸的文件类型，支持所有文件格式
 */
@Slf4j
@Component
public class RdFileStrategy extends AbstractFileStrategy {

    private final String fileType = FilesFileType.RD_FILE.name();

    /**
     * 获取支持的文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getSupportedFileType() {
        return fileType;
    }

    /**
     * 获取文件类型
     *
     * @return 文件类型
     */
    @Override
    public String getFileType() {
        return fileType;
    }

    /**
     * 验证文件数量
     *
     * @param files 上传的文件数组
     */
    @Override
    public void validateFileCount(MultipartFile[] files) {
        if (files == null || files.length != 1) {
            throw new ServiceException("请上传一个研发文件");
        }
    }

    /**
     * 验证文件格式
     * 研发文件支持所有文件格式，不做限制
     *
     * @param file 上传的文件
     */
    @Override
    public void validateFileFormat(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }
        
        // 研发文件支持所有格式，只检查文件是否为空
        if (file.getSize() == 0) {
            throw new ServiceException("上传文件不能为空文件");
        }
        
        // 检查文件大小限制（100MB）
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("文件大小不能超过100MB");
        }
        
        log.info("研发文件格式验证通过: {}, 大小: {}KB", 
                file.getOriginalFilename(), file.getSize() / 1024);
    }

    /**
     * 处理文件上传
     *
     * @param files        上传的文件数组
     * @param productModel 产品型号
     * @param version      版本号
     * @param subType      子类型（研发文件可以为空）
     * @return 处理结果
     */
    @Override
    public AjaxResult handleFileUpload(MultipartFile[] files, ProductModels productModel, String version, String subType) {
        try {
            validateFileCount(files);
            MultipartFile file = files[0];
            validateFileFormat(file);

            // 构建文件路径
            Path filePath = buildFilePath(productModel.getModelCode(), file.getOriginalFilename());
            
            // 保存文件到磁盘
            saveFileToDisk(file, filePath);

            // 创建文件记录
            Files fileRecord = new Files();
            fileRecord.setProductModelId(productModel.getId());
            fileRecord.setFileType(FilesFileType.RD_FILE);
            fileRecord.setSubType(StringUtils.isNotBlank(subType) ? subType : "GENERAL"); // 默认为通用类型
            fileRecord.setVersion(version);
            fileRecord.setStatus(FilesStatus.ACTIVE);
            fileRecord.setFilePath(filePath.toString());
            fileRecord.setFileName(file.getOriginalFilename());

            // 保存到数据库
            filesDao.insert(fileRecord);

            log.info("研发文件上传成功: 产品型号={}, 文件名={}, 版本={}", 
                    productModel.getModelCode(), file.getOriginalFilename(), version);

            return AjaxResult.success("研发文件上传成功", fileRecord);

        } catch (ServiceException e) {
            log.error("研发文件上传失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("研发文件上传异常", e);
            throw new ServiceException("研发文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 构建文件路径
     * 研发文件存储在独立的目录中
     *
     * @param modelCode 产品型号代码
     * @param fileName  文件名
     * @return 文件路径
     */
    @Override
    protected Path buildFilePath(String modelCode, String fileName) {
        return getBaseUploadPath()
                .resolve("rd-files")  // 研发文件专用目录
                .resolve(modelCode)
                .resolve(fileName);
    }

    /**
     * 验证版本号
     * 研发文件的版本号验证相对宽松
     *
     * @param version 版本号
     */
    @Override
    public void validateVersion(String version) {
        if (StringUtils.isBlank(version)) {
            throw new ServiceException("版本号不能为空");
        }
        
        // 研发文件版本号格式相对灵活，只要不为空即可
        if (version.trim().length() > 50) {
            throw new ServiceException("版本号长度不能超过50个字符");
        }
    }

    /**
     * 验证子类型
     * 研发文件的子类型是可选的
     *
     * @param subType 子类型
     */
    @Override
    public void validateSubType(String subType) {
        // 研发文件的子类型是可选的，可以为空
        if (StringUtils.isNotBlank(subType) && subType.length() > 20) {
            throw new ServiceException("子类型长度不能超过20个字符");
        }
    }
}
