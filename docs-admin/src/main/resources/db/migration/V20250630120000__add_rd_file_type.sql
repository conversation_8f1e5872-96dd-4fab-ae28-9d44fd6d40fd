-- =============================================
-- 添加研发文件类型到files表
-- 为研发文件创建独立的文件类型分类
-- =============================================

-- 修改files表的file_type枚举，添加RD_FILE类型
ALTER TABLE files 
MODIFY COLUMN file_type ENUM('BOM','DRAWING','BOM_DRAWING','SOFTWARE','RD_FILE') 
DEFAULT NULL COMMENT '文件类型';

-- 添加研发文件相关的权限菜单
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES 
('研发文件管理', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '文档管理' LIMIT 1) AS temp), 4, 'rd-files', 'docs/files/rd-files/index', 1, 0, 'C', '0', '0', 'files:rd:list', 'documentation', 'admin', NOW(), '', NULL, '研发文件管理菜单');

-- 添加研发文件相关的功能权限
INSERT IGNORE INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES 
('研发文件查询', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 1, '', '', 1, 0, 'F', '0', '0', 'files:rd:query', '#', 'admin', NOW(), '', NULL, ''),
('研发文件新增', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 2, '', '', 1, 0, 'F', '0', '0', 'files:rd:add', '#', 'admin', NOW(), '', NULL, ''),
('研发文件修改', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 3, '', '', 1, 0, 'F', '0', '0', 'files:rd:edit', '#', 'admin', NOW(), '', NULL, ''),
('研发文件删除', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 4, '', '', 1, 0, 'F', '0', '0', 'files:rd:remove', '#', 'admin', NOW(), '', NULL, ''),
('研发文件导出', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 5, '', '', 1, 0, 'F', '0', '0', 'files:rd:export', '#', 'admin', NOW(), '', NULL, ''),
('研发文件上传', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 6, '', '', 1, 0, 'F', '0', '0', 'files:rd:upload', '#', 'admin', NOW(), '', NULL, ''),
('研发文件下载', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 7, '', '', 1, 0, 'F', '0', '0', 'files:rd:download', '#', 'admin', NOW(), '', NULL, ''),
('研发文件历史', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE menu_name = '研发文件管理' LIMIT 1) AS temp), 8, '', '', 1, 0, 'F', '0', '0', 'files:rd:history', '#', 'admin', NOW(), '', NULL, '');

-- 为管理员角色分配研发文件权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'files:rd:%';

-- 添加系统配置说明
INSERT IGNORE INTO sys_config (config_name, config_key, config_value, config_type, remark) VALUES 
('研发文件类型版本', 'files.rd.type.version', 'v1.0', 'Y', '研发文件类型支持版本，独立于图纸文件的文件类型管理');
